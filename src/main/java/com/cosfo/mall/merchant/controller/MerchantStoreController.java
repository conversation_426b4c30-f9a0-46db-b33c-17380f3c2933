package com.cosfo.mall.merchant.controller;

import com.cosfo.mall.common.config.GrayReleaseConfig;
import com.cosfo.mall.common.controller.BaseController;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.LoginDTO;
import com.cosfo.mall.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.mall.merchant.model.dto.LoginSuccessReturnDTO;
import com.cosfo.mall.merchant.model.dto.SmsLoginDTO;
import com.cosfo.mall.merchant.model.vo.MerchantStoreVO;
import com.cosfo.mall.merchant.model.vo.LoginVO;
import com.cosfo.mall.merchant.service.MerchantStoreService;
import net.xianmu.common.exception.ProviderException;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 *
 * <AUTHOR>
 * @description
 * @date 2022/5/21 11:34
 */
@RestController
@RequestMapping("/merchant/store")
public class MerchantStoreController extends BaseController {

    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;


    /**
     * 登录接口
     *
     * @param loginDto 账户信息
     * @return
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public ResultDTO login(@RequestBody LoginDTO loginDto) {
        if (grayReleaseConfig.isOldLoginUrlIntercept()) {
            throw new ProviderException("旧登录接口已停用,请重新登陆");
        }
        return merchantStoreService.newLogin(loginDto);
    }

    /**
     * 发送验证码
     *
     * @param phone
     * @param tenantId 租户ID
     * @return
     */
    @RequestMapping(value = "/sendCode", method = RequestMethod.GET)
    public ResultDTO sendCode(String phone, Long tenantId) {
        return merchantStoreService.sendCode(phone, tenantId);
    }

    /**
     * 校验验证码
     *
     * @param phone
     * @return
     */
    @RequestMapping(value = "/examineCode", method = RequestMethod.GET)
    public ResultDTO examineCode(String phone, String code) {
        Boolean result = merchantStoreService.examineCode(phone, code);
        return result ? ResultDTO.success() : ResultDTO.fail(ResultDTOEnum.CODE_ERROR);
//        return ResultDTO.success();
    }

    /**
     * 验证码登录
     *
     * @param smsLoginDTO
     * @return
     */
    @RequestMapping(value = "/smsCodeLogin", method = RequestMethod.POST)
    public ResultDTO<LoginVO> smsCodeLogin(@Valid @RequestBody SmsLoginDTO smsLoginDTO) {
        return merchantStoreService.smsCodeLogin(smsLoginDTO);
    }

    /**
     * 校验用户是否已注册
     *
     * @return
     */
    @RequestMapping(value = "checkRegistered", method = RequestMethod.POST)
    public ResultDTO checkRegistered(@RequestBody MerchantStoreDTO merchantStoreDTO) {
        return merchantStoreService.checkRegistered(merchantStoreDTO);
    }

    /**
     * 提交用户信息
     *
     * @param merchantStoreVO
     * @return
     */
    @RequestMapping(value = "/submitStoreInfo", method = RequestMethod.POST)
    public ResultDTO submitStoreInfo(@RequestBody MerchantStoreVO merchantStoreVO) {
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        BeanUtils.copyProperties(merchantStoreVO, merchantStoreDTO);
        return merchantStoreService.submitStoreInfo(merchantStoreDTO);
    }

    /**
     * 提交用户信息
     *
     * @param merchantStoreVO
     * @return
     */
    @RequestMapping(value = "/resubmitStoreInfo", method = RequestMethod.POST)
    public ResultDTO resubmitStoreInfo(@RequestBody MerchantStoreVO merchantStoreVO) {
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        BeanUtils.copyProperties(merchantStoreVO, merchantStoreDTO);
        return merchantStoreService.resubmitStoreInfo(merchantStoreDTO);
    }

    @RequestMapping(value = "/getBillInfo", method = RequestMethod.GET)
    public ResultDTO getBillInfo() {
        return merchantStoreService.getBillInfo(getRequestContextInfoDTO());
    }

    /**
     * desc: 退出登录
     *
     * @return
     */
    @RequestMapping(value = "/login-out", method = RequestMethod.POST)
    public ResultDTO<Boolean> loginOut() {
        LoginContextInfoDTO loginContextInfoDTO = getRequestContextInfoDTO();
        return ResultDTO.success(merchantStoreService.newLoginOut(loginContextInfoDTO));
    }

    /**
     * 切换门店
     *
     * @param merchantStoreVO
     * @return
     */
    @RequestMapping(value = "/change-store", method = RequestMethod.POST)
    public ResultDTO<LoginSuccessReturnDTO> changeStore(@RequestBody MerchantStoreVO merchantStoreVO) {
        LoginContextInfoDTO loginContextInfoDTO = getRequestContextInfoDTO();
        return merchantStoreService.newChangeStore(merchantStoreVO.getId(), loginContextInfoDTO);
    }

}
