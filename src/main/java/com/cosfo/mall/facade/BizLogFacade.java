package com.cosfo.mall.facade;

import com.cosfo.common.util.RpcResponseUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.client.provider.BizLogQueryProvider;
import net.summerfarm.common.client.req.bizlog.QueryBizLogReq;
import net.summerfarm.common.client.resp.bizlog.BizLogListResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/12/26 20:40
 * @Description:
 */
@Service
public class BizLogFacade {
    @DubboReference
    private BizLogQueryProvider bizLogQueryProvider;

    public PageInfo<BizLogListResp> listBizLog(QueryBizLogReq req) {
        if (Objects.isNull(req)) {
            throw new BizException("查询日志参数为空！");
        }
        DubboResponse<PageInfo<BizLogListResp>> dubboResponse = bizLogQueryProvider.listBizLog(req);
        return RpcResponseUtil.handler(dubboResponse);
    }

}
