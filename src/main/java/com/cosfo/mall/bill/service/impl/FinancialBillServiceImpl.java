package com.cosfo.mall.bill.service.impl;

import com.cosfo.mall.bill.mapper.FinancialBillCredentialsMapper;
import com.cosfo.mall.bill.mapper.FinancialBillItemMapper;
import com.cosfo.mall.bill.mapper.FinancialBillMapper;
import com.cosfo.mall.bill.mapper.FinancialBillRuleMapper;
import com.cosfo.mall.bill.model.dto.BillCredentialsDTO;
import com.cosfo.mall.bill.model.dto.FinancialBillDTO;
import com.cosfo.mall.bill.model.dto.FinancialBillRuleDTO;
import com.cosfo.mall.bill.model.po.FinancialBill;
import com.cosfo.mall.bill.model.po.FinancialBillCredentials;
import com.cosfo.mall.bill.model.po.FinancialBillItem;
import com.cosfo.mall.bill.model.po.FinancialBillRule;
import com.cosfo.mall.bill.model.vo.FinancialBillCredentialsVO;
import com.cosfo.mall.bill.model.vo.FinancialBillVO;
import com.cosfo.mall.bill.service.FinancialBillService;
import com.cosfo.mall.common.constants.BillTypeEnum;
import com.cosfo.mall.common.constants.BusinessTypeEnum;
import com.cosfo.mall.common.constants.CredentialsOperatorTypeEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.AssertCheckParams;
import com.cosfo.mall.common.utils.PageInfoHelper;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/8
 */
@Service
public class FinancialBillServiceImpl implements FinancialBillService {
    @Resource
    private FinancialBillMapper financialBillMapper;
    @Resource
    private FinancialBillRuleMapper financialBillRuleMapper;
    @Resource
    private FinancialBillItemMapper financialBillItemMapper;
    @Resource
    private FinancialBillCredentialsMapper financialBillCredentialsMapper;

    @Override
    public List<FinancialBillDTO> queryFinancialBill(Long storeId, Long tenantId) {
        AssertCheckParams.notNull(storeId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "门店Id不能为空");
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.MERCHANT_INFO_NOT_FOUND.getCode(), "租户Id不能为空");
        List<FinancialBill> financialBills = financialBillMapper.selectByStoreIdAndTenantId(storeId, tenantId);
        List<FinancialBillDTO> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(financialBills)) {
            list = financialBills.stream().map(item -> {
                FinancialBillDTO financialBillDTO = new FinancialBillDTO();
                BeanUtils.copyProperties(item,financialBillDTO);
                return financialBillDTO;
            }).collect(Collectors.toList());
        }

        return list;
    }

    @Override
    public FinancialBillRuleDTO queryBillRule(Long tenantId) {
        AssertCheckParams.notNull(tenantId, ResultDTOEnum.MERCHANT_INFO_NOT_FOUND.getCode(), "租户Id不能为空");
        FinancialBillRule financialBillRule = financialBillRuleMapper.queryTenantBillRule(tenantId);
        if(Objects.isNull(financialBillRule)){
            return null;
        }

        FinancialBillRuleDTO financialBillRuleDTO = new FinancialBillRuleDTO();
        BeanUtils.copyProperties(financialBillRule,financialBillRuleDTO);
        return financialBillRuleDTO;
    }

    @Override
    public ResultDTO<PageInfo<FinancialBillVO>> list(Integer pageSize, Integer pageIndex, Integer status, String year, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        Long storeId = contextInfoDTO.getStoreId();

        // 获取一年的开始结束时间
        String startTime = year + "-01-01 00:00:00";
        String endTime = year + "-12-31 23:59:59";
        PageHelper.startPage(pageIndex,pageSize);
        List<FinancialBill> financialBills = financialBillMapper.list(storeId, tenantId, status, BillTypeEnum.RECEIVABLE_BILL.getCode(), startTime, endTime);
        List<FinancialBillVO> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(financialBills)){
            for(FinancialBill financialBill: financialBills){
                FinancialBillVO financialBillVO = new FinancialBillVO();
                financialBillVO.setBillStartTime(financialBill.getStartTime());
                financialBillVO.setBillEndTime(financialBill.getEndTime());
                financialBillVO.setBillId(financialBill.getId());
                financialBillVO.setBillType(financialBill.getBillType());
                financialBillVO.setReceivablePrice(financialBill.getReceivablePrice());
                list.add(financialBillVO);
            }
        }

        return ResultDTO.success(PageInfoHelper.createPageInfo(list, pageSize));
    }

    @Override
    public ResultDTO<BigDecimal> getYearReceivablePrice(Integer status, String year, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        Long storeId = contextInfoDTO.getStoreId();
        // 获取一年的开始结束时间
        String startTime = year + "-01-01 00:00:00";
        String endTime = year + "-12-31 23:59:59";
        BigDecimal yearReceivablePrice = financialBillMapper.getYearReceivablePrice(storeId, tenantId, status, BillTypeEnum.RECEIVABLE_BILL.getCode(), startTime, endTime);
        return ResultDTO.success(Objects.isNull(yearReceivablePrice) ? BigDecimal.ZERO : yearReceivablePrice);
    }

    @Override
    public ResultDTO<FinancialBillVO> detail(Long billId,LoginContextInfoDTO contextInfoDTO) {
        FinancialBill financialBill = financialBillMapper.selectByPrimaryKey(billId);
        FinancialBillVO financialBillVO = new FinancialBillVO();
        BeanUtils.copyProperties(financialBill,financialBillVO);
        financialBillVO.setBillId(financialBill.getId());
        financialBillVO.setBillStartTime(financialBill.getStartTime());
        financialBillVO.setBillEndTime(financialBill.getEndTime());
        // 订单
        List<FinancialBillItem> orderFinancialBillItems = financialBillItemMapper.selectByBillId(contextInfoDTO.getTenantId(), billId, BusinessTypeEnum.ORDER.getCode());
        financialBillVO.setOrderNum(CollectionUtils.isEmpty(orderFinancialBillItems) ? 0 : orderFinancialBillItems.size());
        financialBillVO.setOrderPrice(financialBill.getOrderReceivablePrice());
        // 售后单
        List<FinancialBillItem> orderAfterSaleFinancialBillItems = financialBillItemMapper.selectByBillId(contextInfoDTO.getTenantId(), billId, BusinessTypeEnum.ORDER.getCode());
        financialBillVO.setOrderAfterSaleNum(CollectionUtils.isEmpty(orderAfterSaleFinancialBillItems) ? 0 : orderAfterSaleFinancialBillItems.size());
        financialBillVO.setOrderAfterSalePrice(financialBill.getOrderAfterSaleTotalPrice());


        // 查询门店提交凭证信息
        FinancialBillCredentials storeFinancialBillCredentials = financialBillCredentialsMapper.selectByBillId(contextInfoDTO.getTenantId(), contextInfoDTO.getStoreId(), billId, CredentialsOperatorTypeEnum.PAYER.getCode());
        if (Objects.nonNull(storeFinancialBillCredentials)) {
            FinancialBillCredentialsVO storeCredentials = new FinancialBillCredentialsVO();
            BeanUtils.copyProperties(storeFinancialBillCredentials, storeCredentials);
            financialBillVO.setStoreCredentials(storeCredentials);
        }

        // 查询品牌方提交凭证信息
        FinancialBillCredentials tenantFinancialBillCredentials = financialBillCredentialsMapper.selectByBillId(contextInfoDTO.getTenantId(), contextInfoDTO.getTenantId(), billId, CredentialsOperatorTypeEnum.PAYEE.getCode());
        if (Objects.nonNull(tenantFinancialBillCredentials)) {
            FinancialBillCredentialsVO tenantCredentials = new FinancialBillCredentialsVO();
            BeanUtils.copyProperties(tenantFinancialBillCredentials, tenantCredentials);
            financialBillVO.setTenantCredentials(tenantCredentials);
        }
        return ResultDTO.success(financialBillVO );
    }

    @Override
    public ResultDTO uploadCredentials(BillCredentialsDTO billCredentialsDTO, LoginContextInfoDTO contextInfoDTO) {
        FinancialBillCredentials financialBillCredentials = financialBillCredentialsMapper.selectByBillId(contextInfoDTO.getTenantId(), contextInfoDTO.getStoreId(), billCredentialsDTO.getBillId(), CredentialsOperatorTypeEnum.PAYER.getCode());
        if (Objects.isNull(financialBillCredentials)) {
            financialBillCredentials = new FinancialBillCredentials();
            financialBillCredentials.setCredentials(billCredentialsDTO.getCredentials());
            financialBillCredentials.setTenantId(contextInfoDTO.getTenantId());
            financialBillCredentials.setCredentialsTime(TimeUtils.dateConvertLocalDateTime(new Date()));
            financialBillCredentials.setRemark(billCredentialsDTO.getRemark());
            financialBillCredentials.setBillId(billCredentialsDTO.getBillId());
            financialBillCredentials.setOperatorType(CredentialsOperatorTypeEnum.PAYER.getCode());
            financialBillCredentials.setOperatorId(contextInfoDTO.getStoreId());
            financialBillCredentialsMapper.insertSelective(financialBillCredentials);
        } else {
            financialBillCredentials.setCredentials(billCredentialsDTO.getCredentials());
            financialBillCredentials.setTenantId(contextInfoDTO.getTenantId());
            financialBillCredentials.setCredentialsTime(TimeUtils.dateConvertLocalDateTime(new Date()));
            financialBillCredentials.setRemark(billCredentialsDTO.getRemark());
            financialBillCredentials.setBillId(billCredentialsDTO.getBillId());
            financialBillCredentials.setOperatorType(CredentialsOperatorTypeEnum.PAYER.getCode());
            financialBillCredentials.setOperatorId(contextInfoDTO.getStoreId());
            financialBillCredentialsMapper.updateByPrimaryKeySelective(financialBillCredentials);
        }

        // 更新下账单状态
        financialBillMapper.uploadStatus(billCredentialsDTO.getBillId(), 1);

        return ResultDTO.success();
    }

    @Override
    public List<FinancialBill> queryNeedPayBill(Long storeId, Long tenantId) {
        return financialBillMapper.queryNeedPayBill(storeId,tenantId);
    }
}
