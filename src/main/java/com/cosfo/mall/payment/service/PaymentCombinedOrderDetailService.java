package com.cosfo.mall.payment.service;

import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;

import java.util.Collection;
import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-04-30
 **/
public interface PaymentCombinedOrderDetailService {

    /**
     * 根据组合明细IDs查询
     *
     * @param tenantId、combinedDetailId
     * @return
     */
    List<PaymentCombinedOrderDetail> selectByCombinedDetailIds(Long tenantId, List<Long> combinedDetailId);
}
