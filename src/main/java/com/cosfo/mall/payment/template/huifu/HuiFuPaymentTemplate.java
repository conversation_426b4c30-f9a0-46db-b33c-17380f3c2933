package com.cosfo.mall.payment.template.huifu;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.config.BusinessTimeConfig;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constants.HuiFuStatusEnums;
import com.cosfo.mall.common.constants.PaySceneEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.model.dto.HuiFuDTO;
import com.cosfo.mall.common.utils.SignatureUtil;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.HuiFuPaymentCloseResponseDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentQueryRequestDTO;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.payment.convert.PaymentConvert;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.template.PayTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.model.po.TenantAuthConnection;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.api.HuiFuApi;
import com.cosfo.mall.wechat.bean.base.Amount;
import com.cosfo.mall.wechat.bean.paymch.WxData;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * @description: 汇付微信间连支付
 * @author: George
 * @date: 2023-08-29
 **/
@Slf4j
@Service
public abstract class HuiFuPaymentTemplate extends PayTemplate {

    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private HuiFuConfig huiFuConfig;
    @Resource
    private PaymentService paymentService;
    @Resource
    private PaymentMapper paymentMapper;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Resource
    private TenantService tenantService;
    @Resource
    private BusinessTimeConfig businessTimeConfig;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    @Override
    protected void preProcess(PaymentRequest request) {
        super.preProcess(request);
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());
        // 校验汇付商户配置信息是否完整
        if (StringUtils.isEmpty(tenantAuthConnectionDTO.getHuifuId()) ||
                StringUtils.isEmpty(tenantAuthConnectionDTO.getSecretKey()) ||
                StringUtils.isEmpty(tenantAuthConnectionDTO.getPublicKey()) ||
                StringUtils.isEmpty(tenantAuthConnectionDTO.getHuifuPublicKey())) {
            throw new BizException("汇付商户信息未配置，请联系管理员");
        }
    }

    @Override
    protected PaymentResult processPay(PaymentRequest request) {
        // 封装调用汇付api所需数据
        HuiFuDTO huiFuDTO = assemblyHuiFuPayRequest(request);
        String result = HuiFuApi.huiFuJsPay(huiFuDTO);
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(result, HuiFuDTO.class);
        HuiFuiPaymentReceive huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuiPaymentReceive.class);

        //插入汇付支付信息
        com.cosfo.mall.order.model.po.HuiFuPayment huiFuPayment = PaymentConvert.convertToHuiFuPayment(huiFuPaymentReceive);
        huiFuPayment.setTenantId(request.getTenantId());
        huiFuPayment.setPaymentId(request.getPaymentId());
        if (request.isCombineRequest()) {
            huiFuPayment.setPaymentId(request.getMasterPaymentId());
        }
        huiFuPaymentMapper.insert(huiFuPayment);

        if (!Objects.equals(HuiFuApi.PAY_SUCCESS_CODE, huiFuPaymentReceive.getResp_code())) {
            log.error("发起汇付支付失败，失败原因：{},具体原因：{} ", huiFuPaymentReceive.getResp_desc(), huiFuPaymentReceive.getBank_message());
            throw new ProviderException("本次支付交易失败, 请稍后重试");
        }

        //返回前端拉起支付数据
        PaymentResult paymentResult = new PaymentResult();
        paymentResult.setSuccess(true);
        paymentResult.setPrepayId(huiFuPayment.getPayInfo());
        paymentResult.setQrCode(huiFuPayment.getQrCode());
        return paymentResult;
    }

    @Override
    protected void onSuccess(PaymentRequest request, PaymentResult result) {
        // 如果是组合支付 则冻结支付单即可 等待后续支付回调或者订单超时取消处理最终结果
        if (request.isCombineRequest()) {
            // 更新子支付单的状态为冻结中
            Long paymentId = request.getPaymentId();
            int updateStatus = paymentCombinedDetailService.updateStatus(
                    paymentId,
                    PaymentEnum.Status.DEALING.getCode(),
                    PaymentEnum.Status.WAITING.getCode());
            if (updateStatus != 1) {
                log.error("支付单：[{}]由待支付变更程处理中异常", paymentId);
                throw new ProviderException("本次支付交易失败，请稍后再试");
            }
            return;
        }

        Long paymentId = request.getPaymentId();
        // 乐观更新支付单为锁定状态
        int updateStatus = paymentMapper.updateStatus(paymentId, PaymentEnum.Status.DEALING.getCode(), PaymentEnum.Status.WAITING.getCode());
        if (updateStatus != 1) {
            log.error("支付单：[{}]由待支付变更成处理中失败", paymentId);
            throw new ProviderException("本次支付交易失败，请稍后再试");
        }
    }

    /**
     * 组装汇付支付请求的参数
     *
     * @param request
     * @return
     */
    private HuiFuDTO assemblyHuiFuPayRequest(PaymentRequest request) {
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectByTenantId(request.getTenantId());
        HuiFuPaymentDTO huiFuPayRequestDTO = new HuiFuPaymentDTO();
        huiFuPayRequestDTO.setReqSeqId(request.getPaymentNo());
        huiFuPayRequestDTO.setReqDate(TimeUtils.changeDate2String(new Date(), Constants.HUIFU_DATE));
        huiFuPayRequestDTO.setHuiFuId(tenantAuthConnection.getHuifuId());

        Long paymentId = request.getPaymentId();
        if (request.isCombineRequest()) {
            paymentId = request.getMasterPaymentId();
        }
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        huiFuPayRequestDTO.setPaymentId(paymentId);
        long paymentExpireTime = businessTimeConfig.getPaymentExpireTime();
        log.info("支付单：[{}]支付过期时间为：[{}]分钟", request.getPaymentNo(), paymentExpireTime);
        LocalDateTime payDeadline = payment.getCreateTime().plusMinutes(paymentExpireTime);
        String expireTime = payDeadline.format(DateTimeFormatter.ofPattern(Constants.HUIFU_EXPIRE_DATE));
        huiFuPayRequestDTO.setTimeExpire(expireTime);
        huiFuPayRequestDTO.setTradeType(request.getTradeType());

        Amount amount = new Amount();
        amount.setTotalPrice(request.getTransAmt());
        // 交易金额
        String trans_amt = String.valueOf(amount.getTotalPrice().setScale(2, RoundingMode.HALF_UP));
        huiFuPayRequestDTO.setTransAmt(trans_amt);
        huiFuPayRequestDTO.setGoodsDesc(StringUtils.isNotBlank(request.getPaymentDesc()) ? request.getPaymentDesc() : TimeUtils.changeDate2String(new Date(), Constants.HUIFU_EXPIRE_DATE) + Constants.HUIFU_PRODUCTS_DESC);
        huiFuPayRequestDTO.setNotifyUrl(notifyDomain + "/pay-notify/huifu-pay");
        // 是否延时交易
        huiFuPayRequestDTO.setDelayAcctFlag("Y");
        huiFuPayRequestDTO.setChannelNo("00005016");
        huiFuPayRequestDTO.setPayScene(PaySceneEnum.DOWN_LINE.getCode());
        // 微信扩展信息
        WxData wxData = new WxData();
        wxData.setSub_appid(request.getH5Request() ? tenantAuthConnection.getOaAppId() : tenantAuthConnection.getAppId());
        wxData.setOpenid(payment.getSpOpenid());
        //wxData.setSub_openid(paymentDTO.getSubOpenid());
        huiFuPayRequestDTO.setWxData(JSONObject.toJSONString(wxData));

        //参数加签
        String privateKey = tenantAuthConnection.getSecretKey();
        String signRes = SignatureUtil.bodySign(huiFuPayRequestDTO, privateKey);

        //请求汇付支付
        HuiFuDTO huiFuDTO = new HuiFuDTO(huiFuPayRequestDTO);
        huiFuDTO.setSys_id(huiFuPayRequestDTO.getHuiFuId());
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSign(signRes);
        return huiFuDTO;
    }

    @Override
    public PaymentResult queryLastPaymentResult(PaymentRequest paymentRequest) {
        Long paymentId = paymentRequest.getPaymentId();
        Long tenantId = paymentRequest.getTenantId();
        com.cosfo.mall.order.model.po.HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<com.cosfo.mall.order.model.po.HuiFuPayment>().eq(com.cosfo.mall.order.model.po.HuiFuPayment::getPaymentId, paymentId));
        if (huiFuPayment == null) {
            return null;
        }

        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectByTenantId(tenantId);

        HuiFuPaymentQueryRequestDTO huiFuPaymentQueryRequestDTO = new HuiFuPaymentQueryRequestDTO();
        huiFuPaymentQueryRequestDTO.setOrgReqDate(huiFuPayment.getReqDate());
        huiFuPaymentQueryRequestDTO.setHuiFuId(huiFuPayment.getHuifuId());
        huiFuPaymentQueryRequestDTO.setOrgHfSeqId(huiFuPayment.getHfSeqId());
        if (StringUtils.isEmpty(huiFuPaymentQueryRequestDTO.getOrgHfSeqId())) {
            huiFuPaymentQueryRequestDTO.setOrgReqSeqId(huiFuPayment.getReqSeqId());
        }
        HuiFuDTO<HuiFuPaymentQueryRequestDTO> huiFuDTO = new HuiFuDTO<>(huiFuPaymentQueryRequestDTO);
        String sign = SignatureUtil.sign(JSONObject.toJSONString(huiFuDTO.getData()), tenantAuthConnection.getSecretKey());
        huiFuDTO.setSign(sign);
        huiFuDTO.setProduct_id(huiFuConfig.getProductId());
        huiFuDTO.setSys_id(huiFuPayment.getHuifuId());
        log.info("汇付支付查询请求参数：{}", JSONObject.toJSONString(huiFuDTO));
        String result = HuiFuApi.queryPaymentResult(huiFuDTO);
        HuiFuDTO huiFuResponseDTO = JSONObject.parseObject(result, HuiFuDTO.class);
        HuiFuiPaymentReceive huiFuPaymentReceive = JSONObject.parseObject(JSONObject.toJSONString(huiFuResponseDTO.getData()), HuiFuiPaymentReceive.class);
        OrderPayResultDTO orderPayResultDTO = PaymentConvert.convertToOrderPayResultDTO(huiFuPaymentReceive);
        if (Objects.nonNull(orderPayResultDTO) && Objects.nonNull(orderPayResultDTO.getTrans_stat())) {
            orderPayResultDTO.setPaymentId(paymentId);
            orderPayResultDTO.setPaymentStatus(PaymentEnum.Status.getStatusByHuifuStat(orderPayResultDTO.getTrans_stat()).getCode());
        }
        PaymentResult paymentResult = new PaymentResult();
        paymentResult.setOrderPayResultDTO(orderPayResultDTO);
        return paymentResult;
    }

    @Override
    protected void paySuccess(PaymentRequest request, PaymentResult result) {
        // 调用原有的支付成功的操作
        OrderPayResultDTO orderPayResultDTO = result.getOrderPayResultDTO();
        paymentService.huifuPaySuccess(orderPayResultDTO, request.getPaymentId());
    }

    @Override
    public boolean callClosePayRequest(PaymentRequest request) {
        // 这里主要是汇付的接口不允许一分钟内的交易被关闭
        if (request.getPaymentCreateTime().plusMinutes(1).plusSeconds(10).isAfter(LocalDateTime.now())) {
            log.warn("支付单：[{}]，创建时间：[{}]，距离现在不足一分钟，不允许关闭", request.getPaymentNo(), request.getPaymentCreateTime());
            return false;
        }
        Long paymentId = request.getPaymentId();
        // 查询汇付支付单
        com.cosfo.mall.order.model.po.HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<com.cosfo.mall.order.model.po.HuiFuPayment>().eq(com.cosfo.mall.order.model.po.HuiFuPayment::getPaymentId, paymentId));
        // 查询汇付配置信息
        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectByTenantId(request.getTenantId());
        HuiFuPaymentCloseResponseDTO huiFuPaymentCloseResponseDTO = HuiFuApi.huiFuPaymentClose(huiFuPayment, tenantAuthConnection, huiFuConfig);
        HuiFuStatusEnums huiFuStatusEnums = HuiFuStatusEnums.getByCode(huiFuPaymentCloseResponseDTO.getTrans_stat());
        switch (huiFuStatusEnums) {
            // 处理中
            case PROCESSING:
                log.info("支付单：[{}]，正在关单中...", paymentId);
                HuiFuPaymentCloseResponseDTO paymentCloseResponseDTO = HuiFuApi.huiFuPaymentCloseQuery(huiFuPayment, tenantAuthConnection, huiFuConfig);
                return dealCloseQueryResult(paymentCloseResponseDTO, paymentId);
            // 关单成功
            case SUCCESS:
                return true;
            // 关单失败
            case FAILED:
                log.error("关闭原支付单：{}失败", paymentId);
                throw new BizException("当前支付操作失败，请稍后重试");
        }
        return false;
    }

    private boolean dealCloseQueryResult(HuiFuPaymentCloseResponseDTO huiFuPaymentCloseResponseDTO, Long paymentId) {
        HuiFuStatusEnums huiFuStatusEnums = HuiFuStatusEnums.getByCode(huiFuPaymentCloseResponseDTO.getTrans_stat());
        switch (huiFuStatusEnums) {
            // 处理中
            case PROCESSING:
                throw new BizException("当前支付操作失败，请稍后重试");
                // 关单成功
            case SUCCESS:
                return true;
            // 关单失败
            case FAILED:
                log.error("关闭原支付单：[{}]失败", paymentId);
                throw new BizException("当前支付操作失败，请稍后重试");
        }

        return false;
    }
}
