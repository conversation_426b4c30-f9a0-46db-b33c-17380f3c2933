package com.cosfo.mall.payment.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.mall.payment.dao.RefundAcctSplitDetailDao;
import com.cosfo.mall.payment.mapper.RefundAcctSplitDetailMapper;
import com.cosfo.mall.payment.model.po.RefundAcctSplitDetail;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
@Service
public class RefundAcctSplitDetailDaoImpl extends ServiceImpl<RefundAcctSplitDetailMapper, RefundAcctSplitDetail> implements RefundAcctSplitDetailDao {

    @Override
    public List<RefundAcctSplitDetail> queryByRefundId(Long refundId, Long tenantId) {
        LambdaQueryWrapper<RefundAcctSplitDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RefundAcctSplitDetail::getRefundId, refundId);
        queryWrapper.eq(RefundAcctSplitDetail::getTenantId, tenantId);
        return list(queryWrapper);
    }

    @Override
    public void saveBatch(List<RefundAcctSplitDetail> refundAcctSplitDetails) {
        getBaseMapper().saveBatch(refundAcctSplitDetails);
    }
}
