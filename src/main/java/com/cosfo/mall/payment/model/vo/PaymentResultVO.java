package com.cosfo.mall.payment.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 支付结果对象
 * @author: <PERSON>
 * @date: 2023-09-11
 **/
@Data
public class PaymentResultVO {

    /**
     * 预支付id
     */
    private String prepayId;

    /**
     * 时间戳
     */
    private String timeStamp;
    /**
     * 随机字符串
     */
    private String nonceStr;
    /**
     * 小程序下单接口返回的prepay_id参数值，提交格式如：prepay_id=***
     */
    @JsonProperty("package")
    private String packageStr;
    /**
     * 签名方式
     */
    private String signType;
    /**
     * 签名
     */
    private String paySign;

    /**
     * 公众号appId
     */
    private String oaAppId;

    /**
     * qrCode
     */
    private String qrCode;

    /**
     * 订单号集合
     */
    private List<String> orderNos;

    /**
     * 交易金额
     */
    private BigDecimal transAmt;

    /**
     * 支付类型标记 0:未明确；1:小程序汇付插件支付
     */
    private Integer payTypeFlag;
    /**
     * 线下 支付凭证
     */
    private String paymentReceipt;
}
