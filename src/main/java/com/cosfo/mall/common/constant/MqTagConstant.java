package com.cosfo.mall.common.constant;

import com.cosfo.mall.common.context.binlog.DbTableName;

/**
 * @desc tag常量
 * <AUTHOR>
 * @date 2023/1/11 14:38
 */
public class MqTagConstant {

    /**
     * 具体业务的tag 不要直接用 例如 closeOrder...
     */
    public static final String TAG_DEMO = "tag_demo";

    /**
     * 自营仓冻结库存 tag
     */
    public static final String TAG_SELF_SUPPLY_ORDER_OCCUPY = "tag_saas_self_supply_order_occupy";

    /**
     * cosfo售后单表
     */
    public static final String ORDER_AFTER_SALE = "order_after_sale";

    public static final String COSFO_BINLOG_TAG = DbTableName.ORDER_AFTER_SALE + "||" +
            DbTableName.PAYMENT + "||" +
            DbTableName.REFUND;

    /**
     * 订单分账
     */
    public static final String ORDER_PROFIT_SHARING = "order_profit_sharing";
}
