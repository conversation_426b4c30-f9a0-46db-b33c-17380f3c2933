package com.cosfo.mall.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/17
 */
@Configuration
@Data
public class FanTaiPaymentConfig {
    /**
     * 帆台支付账号
     */
    @Value(value = "${fantai.payment.mchid}")
    private String mchId;
    /**
     * 帆台支付密钥
     */
    @Value(value = "${fantai.payment.pay-secret}")
    private String paySecret;
    /**
     * 帆台支付证书
     */
    @Value(value = "${fantai.payment.pay-cert-path}")
    private String payCertPath;
}
